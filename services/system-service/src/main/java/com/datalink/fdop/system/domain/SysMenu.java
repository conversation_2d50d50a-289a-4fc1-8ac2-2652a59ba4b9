package com.datalink.fdop.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.datalink.fdop.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

/**
 * 菜单权限表 sys_menu
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@TableName(value = "sys_menu")
public class SysMenu extends BaseEntity {

    /**
     * 菜单ID
     */
    @TableId(value = "menu_id")
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "菜单ID", required = true)
    private Long menuId;

    /**
     * 页面类型
     */
    @ApiModelProperty(value = "页面类型（0普通页面 1永洪页面）", required = true)
    private String pageType;

    /**
     * 报表id
     */
    @ApiModelProperty(value = "报表ID", required = true)
    private String reportId;

    /**
     * 菜单编码
     */
    @ApiModelProperty(value = "菜单编码", required = true)
    @NotBlank(message = "菜单编码不能为空")
    @Pattern(regexp = "[a-zA-Z0-9_]{1,60}", message = "编码只能是数字、字母或一些特殊符号")
    @Size(min = 0, max = 60, message = "菜单编码长度不能超过50个字符")
    private String menuCode;

    /**
     * 菜单名称
     */
    @ApiModelProperty(value = "菜单名称")
    @NotBlank(message = "菜单编码不能为空")
    @Size(min = 0, max = 60, message = "菜单名称长度不能超过50个字符")
    private String menuName;

    /**
     * 父菜单名称
     */
    @ApiModelProperty(value = "父菜单名称", hidden = true)
    @TableField(exist = false)
    private String parentName;

    /**
     * 父菜单ID
     */
    @ApiModelProperty(value = "父菜单ID", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentId;

    /**
     * 显示顺序
     */
    @ApiModelProperty(value = "显示顺序", required = true)
    @NotNull(message = "显示顺序不能为空")
    private Integer orderNum;

    /**
     * 路由地址
     */
    @ApiModelProperty(value = "路由地址")
    @Size(min = 0, max = 200, message = "路由地址不能超过200个字符")
    private String path;

    /**
     * 组件路径
     */
    @ApiModelProperty(value = "组件路径")
    @Size(min = 0, max = 200, message = "组件路径不能超过255个字符")
    private String component;

    /**
     * 路由参数
     */
    @ApiModelProperty(value = "路由参数")
    private String query;

    /**
     * 是否为外链（0是 1否）
     */
    @ApiModelProperty(value = "是否为外链（0是 1否）", required = true)
    private String isFrame;

    /**
     * 是否缓存（0缓存 1不缓存）
     */
    @ApiModelProperty(value = "是否缓存（0缓存 1不缓存）", required = true)
    private String isCache;

    /**
     * 类型（M目录 C菜单 F按钮）
     */
    @ApiModelProperty(value = "类型（M目录 P页面）", required = true)
    @NotBlank(message = "菜单类型不能为空")
    private String menuType;

    /**
     * 显示状态（0显示 1隐藏）
     */
    @ApiModelProperty(value = "显示状态（0显示 1隐藏）", required = true)
    private String visible;

    /**
     * 菜单状态（0显示 1隐藏）
     */
    @ApiModelProperty(value = "菜单状态（0可用 1不可用）", required = true)
    private String status;

    /**
     * 权限字符串
     */
    @ApiModelProperty(value = "权限字符串")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Size(min = 0, max = 100, message = "权限标识长度不能超过100个字符")
    private String perms;

    /**
     * 菜单图标
     */
    @ApiModelProperty(value = "菜单图标")
    private String icon;

    /**
     * 菜单图标
     */
    @ApiModelProperty(value = "是否默认打开")
    private String defaultOpen="0";

    /**
     * 子菜单
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "子菜单", hidden = true)
    private List<SysMenu> children = new ArrayList<SysMenu>();

}
